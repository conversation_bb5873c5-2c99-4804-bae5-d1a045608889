import { initializeApp } from "firebase/app";
import { getA<PERSON>, GoogleAuth<PERSON><PERSON>ider, signInWithPopup, signOut } from "firebase/auth";
import {
  getFirestore,
  collection,
  doc,
  query,
  orderBy,
  onSnapshot,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc
} from "firebase/firestore";
import {
  getStorage,
  ref as storageRef,
  uploadBytes,
  getDownloadURL
} from "firebase/storage";

import { GoogleGenAI, Type } from "@google/genai";
import { Plant, DiagnosticRecord, GeminiDiagnosis } from '../types';

// This file centralizes all external API interactions.

// --- FIREBASE SETUP ---
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

// Validation des variables d'environnement Firebase
const requiredEnvVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

console.log('🔍 Debug - Variables d\'environnement:');
for (const envVar of requiredEnvVars) {
  const value = import.meta.env[envVar];
  console.log(`${envVar}:`, value ? 'DÉFINIE' : 'MANQUANTE');
  if (!value) {
    console.error(`❌ Variable d'environnement manquante: ${envVar}`);
    console.log('🔍 Toutes les variables disponibles:', import.meta.env);
    throw new Error(`Variable d'environnement manquante: ${envVar}`);
  }
}

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);


// --- GEMINI SETUP ---
// API key must be in environment variables (e.g., .env file)
const geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY;
if (!geminiApiKey) {
    throw new Error("VITE_GEMINI_API_KEY is not set in environment variables");
}
const genAI = new GoogleGenAI({ apiKey: geminiApiKey });
const geminiModel = 'gemini-2.5-flash';

// --- AUTHENTICATION ---
export const provider = new GoogleAuthProvider();
export const signInWithGoogle = async () => {
  try {
    await signInWithPopup(auth, provider);
  } catch (error) {
    console.error("Google Sign-In Error:", error);
  }
};
export const signOutUser = async () => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error("Sign-Out Error:", error);
  }
};

// --- FIRESTORE SERVICE (PLANTS) ---
const plantsCol = (userId: string) => collection(db, 'users', userId, 'plants');

export const getPlants = (userId: string, callback: (plants: Plant[]) => void): (() => void) => {
  const q = query(plantsCol(userId), orderBy('createdAt', 'desc'));
  const unsubscribe = onSnapshot(q, (snapshot) => {
    const plantsData = snapshot.docs.map(docSnap => ({ id: docSnap.id, ...docSnap.data() } as Plant));
    callback(plantsData);
  });
  return unsubscribe;
};

export const getPlant = async (userId: string, plantId: string): Promise<Plant | null> => {
  const plantRef = doc(db, 'users', userId, 'plants', plantId);
  const plantSnap = await getDoc(plantRef);
  return plantSnap.exists() ? { id: plantSnap.id, ...plantSnap.data() } as Plant : null;
};

export const addPlant = async (userId: string, plantData: Omit<Plant, 'id'>) => {
  return await addDoc(plantsCol(userId), {
    ...plantData,
    createdAt: new Date()
  });
};

export const updatePlant = async (userId: string, plantId: string, updatedData: Partial<Plant>) => {
  const plantRef = doc(db, 'users', userId, 'plants', plantId);
  return await updateDoc(plantRef, updatedData);
};

export const deletePlant = async (userId: string, plantId: string) => {
  const plantRef = doc(db, 'users', userId, 'plants', plantId);
  // Note: This doesn't delete subcollections. For a production app, a Cloud Function would be needed for cascading deletes.
  return await deleteDoc(plantRef);
};

// --- FIRESTORE SERVICE (DIAGNOSTIC RECORDS) ---
const diagnosticsCol = (userId: string, plantId: string) => collection(db, 'users', userId, 'plants', plantId, 'diagnostics');

export const getDiagnosticRecords = (userId: string, plantId: string, callback: (records: DiagnosticRecord[]) => void): (() => void) => {
  const q = query(diagnosticsCol(userId, plantId), orderBy('timestamp', 'desc'));
  const unsubscribe = onSnapshot(q, (snapshot) => {
    const recordsData = snapshot.docs.map(docSnap => ({ id: docSnap.id, ...docSnap.data() } as DiagnosticRecord));
    callback(recordsData);
  });
  return unsubscribe;
};

export const addDiagnosticRecord = async (userId: string, plantId: string, recordData: Omit<DiagnosticRecord, 'id'>) => {
  return await addDoc(diagnosticsCol(userId, plantId), recordData);
};

// --- STORAGE SERVICE ---
export const uploadImage = async (file: File, path: string): Promise<string> => {
  const refPath = storageRef(storage, path);
  await uploadBytes(refPath, file);
  return await getDownloadURL(refPath);
};

// --- GEMINI SERVICE ---
const diagnosisSchema = {
    type: Type.OBJECT,
    properties: {
        isHealthy: { type: Type.BOOLEAN, description: "Is the plant generally healthy?" },
        disease: { type: Type.STRING, description: "Name of the disease or pest, or 'Healthy' if none." },
        description: { type: Type.STRING, description: "A detailed description of the diagnosis." },
        treatmentPlan: {
            type: Type.OBJECT,
            properties: {
                steps: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Step-by-step treatment instructions." },
                treatmentFrequencyDays: { type: Type.NUMBER, description: "How often to apply treatment, in days. 0 if one-time." },
                recommendedProducts: {
                    type: Type.ARRAY,
                    items: {
                        type: Type.OBJECT,
                        properties: {
                            name: { type: Type.STRING, description: "Name of the fertilizer or treatment product" },
                            type: { type: Type.STRING, description: "Type of product (fertilizer, fungicide, etc.)" },
                            dosages: {
                                type: Type.OBJECT,
                                properties: {
                                    pulverisateur_1L: { type: Type.STRING, description: "Dosage for 1L spray bottle" },
                                    pulverisateur_5L: { type: Type.STRING, description: "Dosage for 5L spray bottle" },
                                    arrosoir_11L: { type: Type.STRING, description: "Dosage for 11L watering can" },
                                    arrosoir_13L: { type: Type.STRING, description: "Dosage for 13L watering can" },
                                    pulverisateur_16L: { type: Type.STRING, description: "Dosage for 16L large spray bottle" }
                                }
                            },
                            applicationMethod: { type: Type.STRING, description: "How to apply (foliar spray, soil watering, etc.)" },
                            precautions: { type: Type.STRING, description: "Safety precautions and warnings" }
                        }
                    }
                }
            }
        },
        careTips: { type: Type.ARRAY, items: { type: Type.STRING }, description: "General care tips for this plant." }
    }
};

export const analyzePlantImages = async (base64Images: string[], plantName: string): Promise<GeminiDiagnosis> => {
    const imageParts = base64Images.map(imgData => ({
        inlineData: {
            mimeType: 'image/jpeg',
            data: imgData
        }
    }));

    const prompt = `Analysez la ou les images fournies d'une plante ${plantName}. Identifiez toute maladie, parasite ou carence nutritionnelle.

INSTRUCTIONS IMPORTANTES POUR LES DOSAGES :
- Pour chaque produit recommandé, calculez les dosages PRÉCIS pour ces contenants spécifiques :
  * Pulvérisateur 1L : dosage exact en grammes ou millilitres
  * Pulvérisateur 5L : dosage exact en grammes ou millilitres
  * Arrosoir 11L : dosage exact en grammes ou millilitres
  * Arrosoir 13L : dosage exact en grammes ou millilitres
  * Pulvérisateur 16L : dosage exact en grammes ou millilitres

- Basez-vous sur ces engrais et traitements courants avec leurs dosages standards :
  * Sulfate de Fer : 2-3g/L pour pulvérisation foliaire, 15-20g/m² pour arrosage
  * Sulfate de Magnésium : 1 cuillère à café/L pour pulvérisation, 20g/10L pour arrosage
  * Sulfate de Potassium : 0.5-1g/L pour arrosage
  * Urée : 3-4g/L pour pulvérisation diluée
  * Purin d'ortie : 10% (1L purin + 9L eau) pour arrosage, 5% pour pulvérisation
  * Bouillie bordelaise : selon instructions fabricant
  * Soufre mouillable : 5g/L pour pulvérisation

- Donnez des dosages EXACTS et PRATIQUES que l'utilisateur peut mesurer facilement
- Incluez les précautions d'usage et méthodes d'application
- Mentionnez la fréquence d'application recommandée

Fournissez un diagnostic clair, un plan de traitement détaillé avec dosages précis, et des conseils de soin généraux. Si la plante semble en bonne santé, confirmez-le et fournissez des conseils de soins préventifs avec dosages préventifs. Répondez en format JSON selon le schéma fourni. Répondez en français.`;

    const contents = {
        parts: [
            ...imageParts,
            { text: prompt }
        ]
    };

    const response = await genAI.models.generateContent({
        model: geminiModel,
        contents,
        config: {
            responseMimeType: "application/json",
            responseSchema: diagnosisSchema
        }
    });

    try {
        const jsonText = response.text?.trim() || '';
        if (!jsonText) {
            throw new Error("Réponse vide de l'IA");
        }
        return JSON.parse(jsonText) as GeminiDiagnosis;
    } catch (e) {
        console.error("Failed to parse Gemini JSON response:", response.text);
        throw new Error("L'analyse IA a retourné un format invalide.");
    }
};
